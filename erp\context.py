"""
Context management system for ERP - Backward compatibility wrapper
"""
# Import the new modular context components
from .context import (
    AsyncLocalStorage,
    ContextManager,
    context,
    with_environment,
    require_database,
    require_user
)

# Re-export all for backward compatibility
__all__ = [
    'AsyncLocalStorage',
    'ContextManager',
    'context',
    'with_environment',
    'require_database',
    'require_user'
]
